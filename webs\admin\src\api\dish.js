import { http } from '@/utils/request'

// 菜品相关API
export const dishApi = {
  // 获取菜品列表
  getDishes: (params) => http.get('/dishes', params),
  
  // 获取菜品详情
  getDishDetail: (id) => http.get(`/dishes/${id}`),
  
  // 创建菜品
  createDish: (data) => http.post('/dishes', data),
  
  // 更新菜品
  updateDish: (id, data) => http.put(`/dishes/${id}`, data),
  
  // 删除菜品
  deleteDish: (id) => http.delete(`/dishes/${id}`),
  
  // 批量删除菜品
  batchDelete: (ids) => http.delete('/dishes/batch', { data: { ids } }),
  
  // 获取菜品分类
  getCategories: () => http.get('/dishes/categories'),
  
  // 创建分类
  createCategory: (data) => http.post('/dishes/categories', data),
  
  // 更新分类
  updateCategory: (id, data) => http.put(`/dishes/categories/${id}`, data),
  
  // 删除分类
  deleteCategory: (id) => http.delete(`/dishes/categories/${id}`),
  
  // 获取菜品统计
  getDishStatistics: () => http.get('/dishes/statistics'),
  
  // 上传菜品图片
  uploadImage: (file) => {
    const formData = new FormData()
    formData.append('image', file)
    return http.post('/dishes/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}
