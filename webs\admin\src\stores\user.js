import {defineStore} from 'pinia';
import {userApi} from '@/api/user';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('admin_token') || '',
    userInfo: null
  }),

  getters: {
    isLoggedIn: state => !!state.token
  },

  actions: {
    // 登录
    async login(credentials) {
      try {
        // 模拟登录API调用
        const mockResponse = {
          success: true,
          data: {
            token: 'mock-admin-token-' + Date.now(),
            user: {
              id: 1,
              name:
                credentials.username === '13800138000' ? '管理员' : '普通用户',
              username: credentials.username,
              role: credentials.username === '13800138000' ? 'admin' : 'user',
              avatar: 'https://picsum.photos/100/100?random=1',
              phone: credentials.username,
              email: '<EMAIL>',
              permissions:
                credentials.username === '13800138000' ? ['*'] : ['read']
            }
          }
        };

        if (mockResponse.success) {
          this.token = mockResponse.data.token;
          this.userInfo = mockResponse.data.user;

          // 保存到本地存储
          localStorage.setItem('admin_token', this.token);
          localStorage.setItem('admin_user', JSON.stringify(this.userInfo));

          return {success: true};
        } else {
          return {success: false, message: mockResponse.message};
        }
      } catch (error) {
        console.error('登录失败:', error);
        return {success: false, message: '登录失败，请重试'};
      }
    },

    // 退出登录
    logout() {
      this.token = '';
      this.userInfo = null;

      // 清除本地存储
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
    },

    // 初始化用户信息
    initUserInfo() {
      const userStr = localStorage.getItem('admin_user');
      if (userStr) {
        try {
          this.userInfo = JSON.parse(userStr);
        } catch (error) {
          console.error('解析用户信息失败:', error);
          this.logout();
        }
      }
    }
  }
});
